#!/usr/bin/env python3
"""
PostgreSQL Connection Manager for AutoGPT Trader
Enhanced PostgreSQL connection management with stability features
"""

import os
import time
import getpass
from typing import Optional, Any, List, Dict
from pathlib import Path


class PostgreSQLManager:
    """Enhanced PostgreSQL connection manager with stability features"""

    def __init__(self):
        self.connection_pool = None
        self.last_check_time = 0
        self.check_interval = 30  # Check every 30 seconds
        self.max_retries = 3
        self.retry_delay = 2
        self.current_user = None
        
        # Initialize connection pool
        self.initialize()

    def initialize(self):
        """Initialize the PostgreSQL connection manager"""
        try:
            import getpass
            self.current_user = getpass.getuser()
            return self._create_connection_pool()
        except Exception as e:
            print(f"❌ [POSTGRESQL] Connection manager initialization failed: {e}")
            return False

    def _create_connection_pool(self):
        """Create a connection pool for better stability"""
        try:
            import psycopg2
            from psycopg2 import pool
            import os

            # Use environment variables for database connection
            db_host = os.getenv('POSTGRES_HOST', 'localhost')
            db_port = int(os.getenv('POSTGRES_PORT', '5432'))
            db_name = os.getenv('POSTGRES_DB', 'autogpt_trader')
            db_user = os.getenv('POSTGRES_USER', 'trader')
            db_password = os.getenv('POSTGRES_PASSWORD', 'AggressiveTrading2025!')

            self.connection_pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=1,
                maxconn=10,
                host=db_host,
                port=db_port,
                database=db_name,
                user=db_user,
                password=db_password,
                # Connection stability settings
                keepalives=1,
                keepalives_idle=30,
                keepalives_interval=10,
                keepalives_count=5
            )
            print("✅ [POSTGRESQL] Connection pool created successfully")
            return True
        except Exception as e:
            print(f"❌ [POSTGRESQL] Failed to create connection pool: {e}")
            return False

    def get_connection(self):
        """Get a connection from the pool with retry logic"""
        for attempt in range(self.max_retries):
            try:
                if self.connection_pool:
                    return self.connection_pool.getconn()
                else:
                    # No pool, try to create one
                    if not self._create_connection_pool():
                        raise Exception("Failed to create connection pool")

            except Exception as e:
                print(f"⚠️ [POSTGRESQL] Connection attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                else:
                    raise Exception(f"Failed to get connection after {self.max_retries} attempts")

        return None

    def return_connection(self, conn):
        """Return a connection to the pool"""
        try:
            if self.connection_pool and conn:
                self.connection_pool.putconn(conn)
        except Exception as e:
            print(f"⚠️ [POSTGRESQL] Error returning connection: {e}")

    def _recreate_pool(self):
        """Recreate the connection pool"""
        try:
            if self.connection_pool:
                self.connection_pool.closeall()
            self.connection_pool = None
            return self._create_connection_pool()
        except Exception as e:
            print(f"❌ [POSTGRESQL] Failed to recreate connection pool: {e}")
            return False

    def check_connectivity(self):
        """Enhanced connectivity check with caching"""
        import time
        
        current_time = time.time()
        
        # Use cached result if recent
        if current_time - self.last_check_time < self.check_interval:
            return self.connection_pool is not None
        
        try:
            conn = self.get_connection()
            if conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                self.return_connection(conn)
                self.last_check_time = current_time
                return True
            else:
                return False
        except Exception as e:
            print(f"❌ [POSTGRESQL] Connectivity check failed: {e}")
            return False

    def execute_query(self, query, params=None, fetch=False):
        """Execute a query with automatic retry and connection management"""
        for attempt in range(self.max_retries):
            conn = None
            try:
                conn = self.get_connection()
                if not conn:
                    raise Exception("Failed to get database connection")

                cursor = conn.cursor()
                cursor.execute(query, params)
                
                if fetch:
                    result = cursor.fetchall()
                    cursor.close()
                    self.return_connection(conn)
                    return result
                else:
                    conn.commit()
                    cursor.close()
                    self.return_connection(conn)
                    return True

            except Exception as e:
                if conn:
                    try:
                        conn.rollback()
                        self.return_connection(conn)
                    except:
                        pass

                print(f"⚠️ [POSTGRESQL] Query attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                else:
                    raise Exception(f"Query failed after {self.max_retries} attempts: {e}")

        return None

    def create_tables(self):
        """Create required tables for the trading system"""
        tables = [
            """
            CREATE TABLE IF NOT EXISTS neural_memory (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                memory_type VARCHAR(50) NOT NULL,
                data JSONB NOT NULL,
                metadata JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS trading_history (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                exchange VARCHAR(50) NOT NULL,
                symbol VARCHAR(20) NOT NULL,
                side VARCHAR(10) NOT NULL,
                amount DECIMAL(20, 8) NOT NULL,
                price DECIMAL(20, 8) NOT NULL,
                profit_loss DECIMAL(20, 8),
                strategy VARCHAR(100),
                metadata JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS learning_data (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                strategy VARCHAR(100) NOT NULL,
                market_conditions JSONB NOT NULL,
                decision JSONB NOT NULL,
                outcome JSONB NOT NULL,
                performance_metrics JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS system_metrics (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metric_type VARCHAR(50) NOT NULL,
                metric_value DECIMAL(20, 8) NOT NULL,
                metadata JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
        ]

        for table_sql in tables:
            try:
                self.execute_query(table_sql)
                print(f"✅ [POSTGRESQL] Table created/verified successfully")
            except Exception as e:
                print(f"❌ [POSTGRESQL] Failed to create table: {e}")
                return False

        return True

    def close_all_connections(self):
        """Close all connections in the pool"""
        try:
            if self.connection_pool:
                self.connection_pool.closeall()
                self.connection_pool = None
                print("✅ [POSTGRESQL] All connections closed")
        except Exception as e:
            print(f"❌ [POSTGRESQL] Error closing connections: {e}")


# Global instance for backward compatibility
postgresql_manager = PostgreSQLManager()
